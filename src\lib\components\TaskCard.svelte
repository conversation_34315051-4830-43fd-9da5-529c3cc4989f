<script lang="ts">
  import Badge from './ui/Badge.svelte';
  import Button from './ui/Button.svelte';
  import { createEventDispatcher } from 'svelte';
  
  export let task: any;
  export let categories: any[] = [];
  
  const dispatch = createEventDispatcher();
  
  function getCategoryName(categoryId: string | null) {
    if (!categoryId) return null;
    const category = categories.find(c => c.id === categoryId);
    return category?.name || null;
  }
  
  function getPriorityLabel(priority: number) {
    switch (priority) {
      case 3: return 'High';
      case 2: return 'Normal';
      case 1: return 'Low';
      default: return 'Normal';
    }
  }
  
  function getPriorityVariant(priority: number): 'danger' | 'warning' | 'primary' {
    switch (priority) {
      case 3: return 'danger';
      case 2: return 'warning';
      case 1: return 'primary';
      default: return 'warning';
    }
  }
  
  function formatDate(dateString: string | null) {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    if (taskDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (taskDate.getTime() === today.getTime() + 24 * 60 * 60 * 1000) {
      return 'Tomorrow';
    } else if (taskDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  }
  
  function handleToggle() {
    dispatch('toggle', task.id);
  }
  
  function handleEdit() {
    dispatch('edit', task.id);
  }
  
  function handleDelete() {
    dispatch('delete', task.id);
  }
</script>

<div class="mobile-card" class:opacity-70={task.completed}>
  <!-- Task Header -->
  <div class="flex items-start gap-3">
    <input
      type="checkbox"
      class="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
      checked={task.completed}
      on:change={handleToggle}
    />
    <div class="flex-1 min-w-0">
      <a href="/dashboard/tasks/{task.id}" class="block">
        <h3 class="text-sm font-medium text-secondary-900 hover:text-primary-600 transition-colors" 
            class:line-through={task.completed}>
          {task.title}
        </h3>
        {#if task.subtitle}
          <p class="text-xs text-secondary-500 mt-1">{task.subtitle}</p>
        {/if}
      </a>
    </div>
  </div>
  
  <!-- Task Details -->
  <div class="space-y-2 mt-3">
    <div class="mobile-card-row">
      <span class="mobile-card-label">Priority</span>
      <Badge variant={getPriorityVariant(task.priority)} size="sm">
        {getPriorityLabel(task.priority)}
      </Badge>
    </div>
    
    <div class="mobile-card-row">
      <span class="mobile-card-label">Due Date</span>
      <span class="mobile-card-value">{formatDate(task.dueDate)}</span>
    </div>
    
    {#if getCategoryName(task.categoryId)}
      <div class="mobile-card-row">
        <span class="mobile-card-label">Category</span>
        <Badge variant="secondary" size="sm">
          {getCategoryName(task.categoryId)}
        </Badge>
      </div>
    {/if}
  </div>
  
  <!-- Actions -->
  <div class="flex justify-end gap-2 mt-4 pt-3 border-t border-secondary-100">
    <Button variant="ghost" size="sm" on:click={handleEdit}>
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
      </svg>
      Edit
    </Button>
    <Button variant="ghost" size="sm" on:click={handleDelete} class="text-danger-600 hover:text-danger-700 hover:bg-danger-50">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>
      Delete
    </Button>
  </div>
</div>
