<script lang="ts">
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import type { PageData } from './$types';
  import { fade } from 'svelte/transition';

  export let data: PageData;

  let filter = 'all'; // 'all', 'active', 'completed', 'today', 'overdue'
  let searchQuery = '';
  let showDeleteModal = false;
  let taskToDelete: string | null = null;
  let deletingTask = false;

  $: filteredTasks = data.tasks.all.filter(task => {
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) && 
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply status filter
    switch (filter) {
      case 'active':
        return !task.completed;
      case 'completed':
        return task.completed;
      case 'today':
        return data.tasks.today.includes(task);
      case 'overdue':
        return data.tasks.overdue.includes(task);
      default:
        return true;
    }
  });

  function formatDate(dateString: string | null): string {
    if (!dateString) return 'No due date';
    
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'High';
      case 1: return 'Normal';
      default: return 'Low';
    }
  }

  function getCategoryName(categoryId: string | null): string {
    if (!categoryId) return '';
    const category = data.categories.find(c => c.id === categoryId);
    return category?.name || '';
  }

  async function toggleTask(taskId: string, completed: boolean) {
    try {
      if (!completed) {
        // Complete the task
        const response = await fetch(`/api/tasks/${taskId}/complete`, {
          method: 'POST'
        });
        
        if (response.ok) {
          await invalidateAll();
        }
      } else {
        // Uncomplete the task
        const response = await fetch(`/api/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ completed: false, completedAt: null })
        });

        if (response.ok) {
          await invalidateAll();
        }
      }
    } catch (error) {
      console.error('Error toggling task:', error);
    }
  }

  function confirmDeleteTask(taskId: string) {
    taskToDelete = taskId;
    showDeleteModal = true;
  }

  function cancelDelete() {
    showDeleteModal = false;
    taskToDelete = null;
  }

  async function deleteTask() {
    if (!taskToDelete) return;

    deletingTask = true;
    try {
      const response = await fetch(`/api/tasks/${taskToDelete}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await invalidateAll();
        showDeleteModal = false;
        taskToDelete = null;
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    } finally {
      deletingTask = false;
    }
  }
</script>

<svelte:head>
  <title>Tasks - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <div class="header-content">
    <div>
      <h1 class="page-title">Tasks</h1>
      <p class="page-subtitle">Manage your tasks and routines</p>
    </div>
    <a href="/dashboard/tasks/new" class="btn-primary">+ New Task</a>
  </div>
</div>

<div class="filters-section">
  <div class="search-box">
    <input
      type="text"
      placeholder="Search tasks..."
      bind:value={searchQuery}
    />
  </div>
  
  <div class="filter-tabs">
    <button 
      class="filter-tab" 
      class:active={filter === 'all'}
      on:click={() => filter = 'all'}
    >
      All ({data.tasks.all.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'active'}
      on:click={() => filter = 'active'}
    >
      Active ({data.tasks.active.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'today'}
      on:click={() => filter = 'today'}
    >
      Today ({data.tasks.today.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'overdue'}
      on:click={() => filter = 'overdue'}
    >
      Overdue ({data.tasks.overdue.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'completed'}
      on:click={() => filter = 'completed'}
    >
      Completed ({data.tasks.completed.length})
    </button>
  </div>
</div>

<div class="tasks-container">
  {#if filteredTasks.length === 0}
    <div class="empty-state">
      {#if searchQuery}
        <p>No tasks found matching "{searchQuery}"</p>
      {:else}
        <p>You have no tasks in this view.</p>
        <a href="/dashboard/tasks/new" class="btn-primary" style="margin-top: 1rem;">Create a Task</a>
      {/if}
    </div>
  {:else}
    <div class="table-wrapper">
      <table class="task-table">
        <thead>
          <tr>
            <th class="checkbox-cell"></th>
            <th>Task</th>
            <th>Priority</th>
            <th>Due Date</th>
            <th>Category</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredTasks as task (task.id)}
            <tr class:completed={task.completed} transition:fade>
              <td class="checkbox-cell" data-label="Complete">
                <input
                  type="checkbox"
                  class="task-check"
                  checked={task.completed}
                  on:change={() => toggleTask(task.id, task.completed)}
                />
              </td>
              <td data-label="Task">
                <a href="/dashboard/tasks/{task.id}" class="task-title-link">
                  <div class="task-title">{task.title}</div>
                  {#if task.subtitle}
                    <div class="task-subtitle">{task.subtitle}</div>
                  {/if}
                </a>
              </td>
              <td data-label="Priority">
                <span class="priority-badge priority-{getPriorityLabel(task.priority).toLowerCase()}">
                  {getPriorityLabel(task.priority)}
                </span>
              </td>
              <td data-label="Due Date">{formatDate(task.dueDate)}</td>
              <td data-label="Category">
                {#if getCategoryName(task.categoryId)}
                  <span class="category-badge">
                    {getCategoryName(task.categoryId)}
                  </span>
                {/if}
              </td>
              <td data-label="Actions">
                <div class="task-actions">
                  <button
                    class="action-btn edit"
                    on:click={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                    title="Edit Task"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                  </button>
                  <button
                    class="action-btn delete"
                    on:click={() => confirmDeleteTask(task.id)}
                    title="Delete Task"
                  >
                   <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <div class="modal-overlay" on:click={cancelDelete}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h3>Delete Task</h3>
        <button class="modal-close" on:click={cancelDelete}>×</button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this task? This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" on:click={cancelDelete} disabled={deletingTask}>
          Cancel
        </button>
        <button class="btn-danger" on:click={deleteTask} disabled={deletingTask}>
          {#if deletingTask}
            <div class="loading-spinner"></div>
            Deleting...
          {:else}
            Delete Task
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Page Header */
  .page-header {
    margin-bottom: 1.5rem;
  }
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .page-title {
    font-size: 1.5rem;
    font-weight: 600;
  }
  .page-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
  }
  .btn-primary {
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.2s;
  }
  .btn-primary:hover {
    background-color: #0056b3;
  }

  /* Filters Section */
  .filters-section {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .search-box {
    max-width: 100%;
    overflow: hidden;
  }
  .search-box input {
    width: 100%;
    max-width: 100%;
    padding: 0.6rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    box-sizing: border-box;
  }
  .search-box input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
  }
  .filter-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  .filter-tab {
    padding: 0.4rem 0.8rem;
    border: 1px solid #ced4da;
    background: #ffffff;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
  }
  .filter-tab:hover {
    border-color: #007bff;
    color: #007bff;
  }
  .filter-tab.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
  
  /* Task Table */
  .table-wrapper {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden; /* For rounded corners */
  }
  .task-table {
    width: 100%;
    border-collapse: collapse;
  }
  .task-table th, .task-table td {
    padding: 0.9rem 1rem;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
  }
  .task-table thead th {
    font-size: 0.8rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    background-color: #f8f9fa;
  }
  .task-table tbody tr:last-child td {
    border-bottom: none;
  }
  .task-table tbody tr:hover {
    background-color: #f8f9fa;
  }
  tr.completed {
    opacity: 0.7;
  }
  tr.completed .task-title {
    text-decoration: line-through;
  }

  .checkbox-cell { width: 40px; }
  .task-check { width: 16px; height: 16px; }

  .task-title-link { text-decoration: none; color: inherit; }
  .task-title { font-weight: 500; color: #212529; }
  .task-subtitle { font-size: 0.8rem; color: #6c757d; margin-top: 0.15rem; }

  /* Badges */
  .priority-badge, .category-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  .priority-badge.priority-high { background-color: #fde8e8; color: #c53030; }
  .priority-badge.priority-normal { background-color: #fff4e6; color: #dd6b20; }
  .priority-badge.priority-low { background-color: #e6f2ff; color: #007bff; }
  .category-badge { background-color: #e9ecef; color: #495057; }
  
  /* Actions */
  .task-actions { display: flex; gap: 0.5rem; }
  .action-btn {
    padding: 0.3rem;
    border: none;
    background: transparent;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
  }
  .action-btn:hover { color: #007bff; }
  .action-btn.delete:hover { color: #dc3545; }

  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .table-wrapper {
      border: none;
      background-color: transparent;
    }
    .task-table thead {
      display: none; /* Hide table headers on mobile */
    }
    .task-table tbody, .task-table tr, .task-table td {
      display: block;
      width: 100%;
    }
    .task-table tr {
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      margin-bottom: 1rem;
    }
    .task-table td {
      padding: 0.75rem 1rem;
      border: none;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .task-table td:last-child {
      border-bottom: none;
    }
    .task-table td::before {
      content: attr(data-label);
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.7rem;
      color: #6c757d;
      margin-right: 1rem;
    }
    .task-table .checkbox-cell {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    .task-table .checkbox-cell::before {
        display: none;
    }
    .task-table td:first-of-type {
       border-top: none;
    }
  }

  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.2s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .modal-header {
    padding: 1.5rem 1.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .modal-body {
    padding: 1rem 1.5rem;
  }

  .modal-body p {
    margin: 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .modal-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
  }

  .btn-secondary, .btn-danger {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e7eb;
  }

  .btn-danger {
    background-color: #dc2626;
    color: white;
  }

  .btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
  }

  .btn-secondary:disabled, .btn-danger:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
