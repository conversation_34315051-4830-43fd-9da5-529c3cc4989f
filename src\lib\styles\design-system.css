@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  body {
    @apply bg-secondary-50 text-secondary-900 antialiased;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-secondary {
    @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-ghost {
    @apply btn text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500 shadow-sm hover:shadow-md;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm hover:shadow-md;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200/50;
  }
  
  .card-elevated {
    @apply bg-white rounded-xl shadow-medium border border-secondary-200/50 hover:shadow-large transition-shadow duration-300;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200/50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200/50 bg-secondary-50/50;
  }
  
  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  /* Priority Badges */
  .priority-high {
    @apply badge-danger;
  }
  
  .priority-normal {
    @apply badge-warning;
  }
  
  .priority-low {
    @apply badge-primary;
  }
  
  /* Mobile-friendly touch targets */
  @media (pointer: coarse) {
    .btn {
      @apply min-h-[44px] px-6;
    }
    
    .btn-sm {
      @apply min-h-[40px] px-4;
    }
  }
  
  /* Table Components */
  .table-container {
    @apply card overflow-hidden;
  }
  
  .table {
    @apply w-full divide-y divide-secondary-200;
  }
  
  .table-header {
    @apply bg-secondary-50;
  }
  
  .table-header th {
    @apply px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-secondary-200;
  }
  
  .table-row {
    @apply hover:bg-secondary-50 transition-colors duration-150;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900;
  }
  
  /* Mobile Table Cards */
  @media (max-width: 768px) {
    .mobile-card {
      @apply card mb-4 p-4 space-y-3;
    }
    
    .mobile-card-row {
      @apply flex justify-between items-center py-2 border-b border-secondary-100 last:border-b-0;
    }
    
    .mobile-card-label {
      @apply text-xs font-medium text-secondary-500 uppercase tracking-wider;
    }
    
    .mobile-card-value {
      @apply text-sm text-secondary-900 font-medium;
    }
  }
  
  /* Filter Tabs */
  .filter-tabs {
    @apply flex flex-wrap gap-2;
  }
  
  .filter-tab {
    @apply px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .filter-tab:not(.active) {
    @apply bg-white text-secondary-600 border-secondary-300 hover:bg-secondary-50 hover:text-secondary-900 hover:border-secondary-400;
  }
  
  .filter-tab.active {
    @apply bg-primary-600 text-white border-primary-600 shadow-sm;
  }
  
  /* Action Buttons */
  .action-btn {
    @apply p-2 text-secondary-400 hover:text-secondary-600 rounded-lg hover:bg-secondary-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .action-btn-danger {
    @apply action-btn hover:text-danger-600 hover:bg-danger-50;
  }
}
