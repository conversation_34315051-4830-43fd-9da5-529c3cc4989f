<script lang="ts">
  import { goto } from '$app/navigation';
  import type { LayoutData } from './$types';
  import '../../lib/styles/design-system.css';

  export let data: LayoutData;
  let sidebarOpen = false;

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
</script>

<svelte:head>
  <title>Dashboard - Routine Mail</title>
</svelte:head>

<div class="flex min-h-screen bg-secondary-50">
  <!-- Sidebar -->
  <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-secondary-200 transform transition-transform duration-300 ease-in-out md:translate-x-0"
         class:-translate-x-full={!sidebarOpen}
         class:translate-x-0={sidebarOpen}>
    <!-- Sidebar Header -->
    <div class="flex items-center gap-3 px-6 py-6 border-b border-secondary-200">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-semibold text-secondary-900 hover:text-primary-600 transition-colors">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
        </svg>
        <span>Routine Mail</span>
      </a>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2">
      <a href="/dashboard/tasks"
         class="flex items-center gap-3 px-4 py-3 text-secondary-700 rounded-lg hover:bg-secondary-100 hover:text-primary-600 transition-colors font-medium"
         on:click={() => sidebarOpen = false}>
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <span>Tasks</span>
      </a>

      <a href="/dashboard/categories"
         class="flex items-center gap-3 px-4 py-3 text-secondary-700 rounded-lg hover:bg-secondary-100 hover:text-primary-600 transition-colors font-medium"
         on:click={() => sidebarOpen = false}>
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="9" y1="9" x2="15" y2="15"></line>
          <line x1="15" y1="9" x2="9" y2="15"></line>
        </svg>
        <span>Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex items-center gap-3 px-4 py-3 text-secondary-700 rounded-lg hover:bg-secondary-100 hover:text-primary-600 transition-colors font-medium"
         on:click={() => sidebarOpen = false}>
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
          <circle cx="12" cy="12" r="3"></circle>
        </svg>
        <span>Settings</span>
      </a>
    </nav>

    <!-- User Profile -->
    <div class="px-4 py-6 border-t border-secondary-200">
      <div class="space-y-3">
        <div class="text-sm font-medium text-secondary-500 truncate">
          {data.user.email}
        </div>
        <button
          class="w-full px-4 py-2 text-sm font-medium text-secondary-700 bg-secondary-100 rounded-lg hover:bg-secondary-200 transition-colors"
          on:click={handleLogout}>
          Logout
        </button>
      </div>
    </div>
  </aside>

  <!-- Mobile Header -->
  <header class="md:hidden flex items-center justify-between px-4 py-4 bg-white border-b border-secondary-200">
    <a href="/dashboard/tasks" class="flex items-center gap-2 text-lg font-semibold text-secondary-900">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
      </svg>
      <span>Routine Mail</span>
    </a>
    <button
      class="p-2 text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 rounded-lg transition-colors"
      on:click={() => sidebarOpen = !sidebarOpen}>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
    </button>
  </header>

  <!-- Main Content -->
  <div class="flex-1 md:ml-64">
    <main class="px-4 py-6 md:px-8 md:py-10 max-w-7xl mx-auto">
      <slot />
    </main>
  </div>

  <!-- Mobile Overlay -->
  {#if sidebarOpen}
    <div class="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden" on:click={() => sidebarOpen = false}></div>
  {/if}
</div>





